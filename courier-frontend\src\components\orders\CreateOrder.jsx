// courier-frontend/src/pages/orders/CreateOrder.jsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Stepper,
  Step,
  StepLabel,
  CircularProgress,
  Divider,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  useMediaQuery,
  Snackbar,
  IconButton,
  Tooltip,
  Chip // Added missing Chip import
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  LocationOn,
  Person,
  Description,
  Payment,
  Check,
  Help,
  ArrowBack,
  Save,
  Info,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import {
  collection,
  addDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
} from 'firebase/firestore';
import { db } from '../../firebase';
import { useAuth } from '../../context/AuthContext';
import { generateNumericOrderId } from '../../utils/orderUtils';
import { toast } from 'react-toastify';
import AdminLayout from "../layout/AdminLayout";
import EnhancedMap from '../maps/EnhancedMap';
import {
  GOOGLE_MAPS_API_KEY,
  DEFAULT_CENTER,
  createMapOptions,
  calculateDistanceWithAPI,
  createAdvancedMarker,
  clearMarkers,
  GOOGLE_MAPS_LIBRARIES
} from '../../utils/googleMaps';

const validateEmail = (email) => {
  const re = /\S+@\S+\.\S+/;
  return re.test(email);
};

const validatePhone = (phone) => {
  const re = /^\d{10}$/; // basic 10-digit number check
  return re.test(phone);
};

const steps = ['Customer Details', 'Package Details', 'Delivery Details', 'Payment', 'Confirmation'];

const packageTypes = [
  { value: 'document', label: 'Document', basePrice: 10 },
  { value: 'parcel', label: 'Parcel', basePrice: 15 },
  { value: 'fragile', label: 'Fragile', basePrice: 20 },
  { value: 'food', label: 'Food', basePrice: 18 },
  { value: 'electronics', label: 'Electronics', basePrice: 25 },
  { value: 'other', label: 'Other', basePrice: 15 },
];

const priorityOptions = [
  { value: 'standard', label: 'Standard (1-2 days)', multiplier: 1 },
  { value: 'express', label: 'Express (Same day)', multiplier: 1.5 },
  { value: 'same-day', label: 'Urgent (3-4 hours)', multiplier: 2 },
];

const paymentMethods = [
  { value: 'cash', label: 'Cash on Delivery' },
  { value: 'card', label: 'Credit/Debit Card' },
  { value: 'bank', label: 'Bank Transfer' },
  { value: 'wallet', label: 'Digital Wallet' },
];

const CreateOrder = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [loadingMap, setLoadingMap] = useState(false);
  const [couriers, setCouriers] = useState([]);

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [saveDraftDialogOpen, setSaveDraftDialogOpen] = useState(false);
  const [mapReady, setMapReady] = useState(false);
  const [geocodeResults, setGeocodeResults] = useState({ pickup: null, delivery: null });
  const [calculatePriceClicked, setCalculatePriceClicked] = useState(false);
  const [helpDialogOpen, setHelpDialogOpen] = useState(false);
  const [helpContent, setHelpContent] = useState({
    title: '',
    content: '',
  });

  const [orderData, setOrderData] = useState({
    // Customer Details
    customerName: '',
    customerEmail: '',
    customerPhone: '',

    // Package Details
    packageType: '',
    productName: '',
    packageWeight: '',
    packageDimensions: '',
    packageDescription: '',

    // Delivery Details
    pickupAddress: '',
    deliveryAddress: '',
    deliveryInstructions: '',
    priority: 'standard',
    courierId: '',
    estimatedDistance: '',

    // Payment Details
    paymentMethod: 'cash',
    amount: '',

    // System Fields
    status: 'pending',
    createdBy: user?.uid || '',
    createdAt: null,
    updatedAt: null,
  });

  const [errors, setErrors] = useState({});
  const [mapCenter, setMapCenter] = useState(DEFAULT_CENTER);
  const [mapInstance, setMapInstance] = useState(null);
  const [advancedMarkers, setAdvancedMarkers] = useState([]);
  const [directionsRenderer, setDirectionsRenderer] = useState(null);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'info',
  });

  useEffect(() => {
    fetchCouriers();

    // Check for draft
    const savedDraft = localStorage.getItem('orderDraft');
    if (savedDraft) {
      const draftData = JSON.parse(savedDraft);
      setSaveDraftDialogOpen(true);
    }

    // Clean up function
    return () => {
      if (activeStep < steps.length - 1) {
        saveDraft();
      }
    };
  }, []);

  // Auto-calculate price when required fields are filled
  useEffect(() => {
    if (orderData.packageType &&
        orderData.packageWeight &&
        orderData.priority &&
        orderData.estimatedDistance) {
      calculatePrice();
    }
  }, [orderData.packageType, orderData.packageWeight, orderData.priority, orderData.estimatedDistance]);

  // Update map when addresses change
  useEffect(() => {
    if (mapReady && orderData.pickupAddress && orderData.deliveryAddress) {
      updateMapWithAddresses();
    }
  }, [mapReady, orderData.pickupAddress, orderData.deliveryAddress]);

  const fetchCouriers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/orders/available-couriers', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user?.accessToken}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCouriers(data.couriers || []);
          if (data.couriers.length === 0) {
            toast.warning('No couriers available at the moment.');
          }
        } else {
          throw new Error(data.error || 'Failed to fetch couriers');
        }
      } else {
        await fetchCouriersFromFirestore();
      }
    } catch (error) {
      console.error('Error fetching couriers from API:', error);
      await fetchCouriersFromFirestore();
    } finally {
      setLoading(false);
    }
  };

  const fetchCouriersFromFirestore = async () => {
    try {
      const couriersRef = collection(db, 'users');
      const q = query(couriersRef, where('role', '==', 'courier'));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const couriersList = querySnapshot.docs.map(doc => ({
          id: doc.id,
          name: doc.data().name || doc.data().displayName || doc.data().email || `Courier ${doc.id.substring(0, 5)}`,
          email: doc.data().email,
          phone: doc.data().phone,
          status: doc.data().status || 'available',
          assignedOrdersCount: (doc.data().assignedOrders || []).length,
          isOnline: doc.data().isOnline || false,
          ...doc.data()
        }));

        couriersList.sort((a, b) => {
          if (a.status === 'available' && b.status !== 'available') return -1;
          if (a.status !== 'available' && b.status === 'available') return 1;
          return a.assignedOrdersCount - b.assignedOrdersCount;
        });

        setCouriers(couriersList);

        if (couriersList.length === 0) {
          toast.warning('No couriers found. Please add couriers first.');
        }
      } else {
        setCouriers([]);
        toast.warning('No couriers available. Please add couriers first.');
      }
    } catch (error) {
      console.error('Error fetching couriers from Firestore:', error);
      setCouriers([]);
      toast.error('Failed to load couriers');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setOrderData(prev => ({
      ...prev,
      [name]: value
    }));

    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const showHelp = (title, content) => {
    setHelpContent({ title, content });
    setHelpDialogOpen(true);
  };

  const validateStep = () => {
    const newErrors = {};

    if (activeStep === 0) {
      if (!orderData.customerName) newErrors.customerName = 'Customer name is required';
      if (orderData.customerEmail && !validateEmail(orderData.customerEmail)) {
        newErrors.customerEmail = 'Please enter a valid email address';
      }
      if (!orderData.customerPhone) {
        newErrors.customerPhone = 'Customer phone is required';
      } else if (!validatePhone(orderData.customerPhone)) {
        newErrors.customerPhone = 'Please enter a valid phone number';
      }
    } else if (activeStep === 1) {
      if (!orderData.packageType) newErrors.packageType = 'Package type is required';
      if (!orderData.packageWeight) {
        newErrors.packageWeight = 'Package weight is required';
      } else if (parseFloat(orderData.packageWeight) <= 0) {
        newErrors.packageWeight = 'Weight must be greater than 0';
      }
    } else if (activeStep === 2) {
      if (!orderData.pickupAddress) newErrors.pickupAddress = 'Pickup address is required';
      if (!orderData.deliveryAddress) newErrors.deliveryAddress = 'Delivery address is required';
      if (orderData.pickupAddress === orderData.deliveryAddress) {
        newErrors.deliveryAddress = 'Delivery address must be different from pickup address';
      }
    } else if (activeStep === 3) {
      if (!orderData.amount) newErrors.amount = 'Amount is required';
      else if (parseFloat(orderData.amount) <= 0) newErrors.amount = 'Amount must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      saveDraft();
      setActiveStep(prevStep => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prevStep => prevStep - 1);
  };

  const saveDraft = () => {
    localStorage.setItem('orderDraft', JSON.stringify(orderData));
  };

  const loadDraft = () => {
    const savedDraft = localStorage.getItem('orderDraft');
    if (savedDraft) {
      setOrderData(JSON.parse(savedDraft));
      setNotification({
        open: true,
        message: 'Draft loaded successfully',
        severity: 'success'
      });
    }
    setSaveDraftDialogOpen(false);
  };

  const clearDraft = () => {
    localStorage.removeItem('orderDraft');
    setSaveDraftDialogOpen(false);
  };

  const updateMapWithAddresses = async () => {
    try {
      setLoadingMap(true);

      // Check if Google Maps is available
      if (!window.google || !window.google.maps) {
        console.warn('Google Maps not loaded, skipping map update');
        setLoadingMap(false);
        return;
      }

      const geocoder = new window.google.maps.Geocoder();

      // Enhanced geocoding with better error handling
      const geocodeAddress = (address, addressType) => {
        return new Promise((resolve, reject) => {
          if (!address || address.trim() === '') {
            reject(new Error(`${addressType} address is empty`));
            return;
          }

          geocoder.geocode({
            address: address.trim(),
            region: 'IN', // Bias towards India
            componentRestrictions: { country: 'IN' }
          }, (results, status) => {
            if (status === 'OK' && results && results[0]) {
              const location = results[0].geometry.location;
              const coords = {
                lat: location.lat(),
                lng: location.lng(),
                formatted_address: results[0].formatted_address,
                place_id: results[0].place_id
              };
              console.log(`Successfully geocoded ${addressType}:`, coords);
              resolve(coords);
            } else {
              console.warn(`Geocoding failed for ${addressType} address:`, status);
              // Use fallback coordinates with slight randomization
              const fallbackCoords = {
                lat: 19.0760 + (Math.random() * 0.1 - 0.05),
                lng: 72.8777 + (Math.random() * 0.1 - 0.05),
                formatted_address: address,
                place_id: null,
                fallback: true
              };

              if (status !== 'ZERO_RESULTS') {
                toast.warning(`Could not find exact location for ${addressType} address. Using approximate location.`);
              }

              resolve(fallbackCoords);
            }
          });
        });
      };

      // Geocode both addresses concurrently for better performance
      const [pickupCoords, deliveryCoords] = await Promise.all([
        geocodeAddress(orderData.pickupAddress, 'pickup'),
        geocodeAddress(orderData.deliveryAddress, 'delivery')
      ]);

      // Enhanced distance calculation with route optimization
      const distanceResult = await calculateDistanceWithAPI(
        { lat: pickupCoords.lat, lng: pickupCoords.lng },
        { lat: deliveryCoords.lat, lng: deliveryCoords.lng }
      );

      const distance = distanceResult.distance.toFixed(2);
      const duration = distanceResult.duration ? Math.round(distanceResult.duration) : null;

      console.log(`Distance calculated: ${distance} km using ${distanceResult.method}`);
      if (duration) {
        console.log(`Estimated duration: ${duration} minutes`);
      }

      // Store geocoding results with additional metadata
      setGeocodeResults({
        pickup: pickupCoords,
        delivery: deliveryCoords,
        distance: distance,
        duration: duration,
        method: distanceResult.method
      });

      // Calculate optimal map center and zoom level
      const bounds = new window.google.maps.LatLngBounds();
      bounds.extend(new window.google.maps.LatLng(pickupCoords.lat, pickupCoords.lng));
      bounds.extend(new window.google.maps.LatLng(deliveryCoords.lat, deliveryCoords.lng));

      const center = bounds.getCenter();
      setMapCenter({
        lat: center.lat(),
        lng: center.lng()
      });

      // Update order data with enhanced information
      setOrderData(prev => ({
        ...prev,
        estimatedDistance: distance,
        estimatedDuration: duration,
        routeOptimized: distanceResult.method !== 'haversine'
      }));

      // Enhanced map rendering with better markers and route display
      if (window.google && window.google.maps && mapInstance) {
        // Clear existing markers
        clearMarkers(advancedMarkers);

        const newMarkers = [];

        // Create enhanced pickup marker
        const pickupMarker = createAdvancedMarker({
          position: pickupCoords,
          map: mapInstance,
          type: 'pickup',
          title: `Pickup: ${pickupCoords.formatted_address || orderData.pickupAddress}`,
          data: {
            address: orderData.pickupAddress,
            coordinates: pickupCoords,
            type: 'pickup'
          },
          status: 'pending'
        });
        if (pickupMarker) newMarkers.push(pickupMarker);

        // Create enhanced delivery marker
        const deliveryMarker = createAdvancedMarker({
          position: deliveryCoords,
          map: mapInstance,
          type: 'delivery',
          title: `Delivery: ${deliveryCoords.formatted_address || orderData.deliveryAddress}`,
          data: {
            address: orderData.deliveryAddress,
            coordinates: deliveryCoords,
            type: 'delivery'
          },
          status: 'pending'
        });
        if (deliveryMarker) newMarkers.push(deliveryMarker);

        setAdvancedMarkers(newMarkers);

        // Clear existing directions
        if (directionsRenderer) {
          directionsRenderer.setMap(null);
        }

        // Enhanced route rendering with better styling
        const directionsService = new window.google.maps.DirectionsService();
        const renderer = new window.google.maps.DirectionsRenderer({
          map: mapInstance,
          suppressMarkers: true, // We use our custom markers
          polylineOptions: {
            strokeColor: '#4285F4',
            strokeWeight: 6,
            strokeOpacity: 0.8,
            geodesic: true
          },
          preserveViewport: false
        });

        // Request optimized route
        directionsService.route({
          origin: new window.google.maps.LatLng(pickupCoords.lat, pickupCoords.lng),
          destination: new window.google.maps.LatLng(deliveryCoords.lat, deliveryCoords.lng),
          travelMode: window.google.maps.TravelMode.DRIVING,
          unitSystem: window.google.maps.UnitSystem.METRIC,
          avoidHighways: false,
          avoidTolls: false,
          optimizeWaypoints: true,
          provideRouteAlternatives: false
        }, (result, status) => {
          if (status === 'OK' && result) {
            renderer.setDirections(result);
            setDirectionsRenderer(renderer);

            // Fit map to show the entire route
            const bounds = new window.google.maps.LatLngBounds();
            result.routes[0].legs.forEach(leg => {
              leg.steps.forEach(step => {
                bounds.extend(step.start_location);
                bounds.extend(step.end_location);
              });
            });
            mapInstance.fitBounds(bounds, { padding: 50 });

            console.log('Route displayed successfully');
          } else {
            console.warn('Directions request failed:', status);
            toast.warning('Could not display route on map');
          }
        });
      }

    } catch (error) {
      console.error('Error updating map:', error);
      toast.error('Failed to load map data');
    } finally {
      setLoadingMap(false);
    }
  };

  const calculatePrice = () => {
    try {
      // Get base price from package type
      const packageTypeObj = packageTypes.find(type => type.value === orderData.packageType);
      const basePrice = packageTypeObj ? packageTypeObj.basePrice : 15;

      // Get priority multiplier
      const priorityObj = priorityOptions.find(option => option.value === orderData.priority);
      const priorityMultiplier = priorityObj ? priorityObj.multiplier : 1;

      // Enhanced weight factor calculation
      const weight = parseFloat(orderData.packageWeight) || 0;
      const weightFactor = Math.max(1, weight * 0.5 + (weight > 10 ? (weight - 10) * 0.3 : 0));

      // Enhanced distance factor with route optimization bonus
      const distance = parseFloat(orderData.estimatedDistance) || 0;
      const distanceFactor = Math.max(1, distance * 0.2 + (distance > 20 ? (distance - 20) * 0.1 : 0));

      // Route optimization bonus (discount for optimized routes)
      const routeOptimizationDiscount = orderData.routeOptimized ? 0.95 : 1;

      // Duration factor (if available)
      const duration = parseFloat(orderData.estimatedDuration) || 0;
      const durationFactor = duration > 0 ? Math.max(1, duration / 60 * 0.1) : 1; // Convert minutes to hours

      // Calculate final price with all factors
      let calculatedPrice = basePrice * weightFactor * distanceFactor * priorityMultiplier * durationFactor * routeOptimizationDiscount;

      // Apply minimum price threshold
      calculatedPrice = Math.max(calculatedPrice, 10);

      // Round to 2 decimal places
      calculatedPrice = parseFloat(calculatedPrice.toFixed(2));

      console.log('Price calculation breakdown:', {
        basePrice,
        weightFactor,
        distanceFactor,
        priorityMultiplier,
        durationFactor,
        routeOptimizationDiscount,
        finalPrice: calculatedPrice
      });

      setOrderData(prev => ({
        ...prev,
        amount: calculatedPrice.toString()
      }));

      setCalculatePriceClicked(true);

      // Show price breakdown to user
      toast.success(`Price calculated: ₹${calculatedPrice} (Base: ₹${basePrice}, Distance: ${distance}km, Weight: ${weight}kg)`);

    } catch (error) {
      console.error('Error calculating price:', error);
      toast.error('Failed to calculate price. Please try again.');
    }
  };

  // Get markers for the enhanced map
  const getOrderMapMarkers = () => {
    const markers = [];

    // Add pickup marker
    if (geocodeResults?.pickup) {
      markers.push({
        position: geocodeResults.pickup,
        type: 'pickup',
        title: `Pickup: ${orderData.pickupAddress}`,
        status: 'pending',
        data: {
          address: orderData.pickupAddress,
          type: 'pickup'
        }
      });
    }

    // Add delivery marker
    if (geocodeResults?.delivery) {
      markers.push({
        position: geocodeResults.delivery,
        type: 'delivery',
        title: `Delivery: ${orderData.deliveryAddress}`,
        status: 'pending',
        data: {
          address: orderData.deliveryAddress,
          type: 'delivery'
        }
      });
    }

    return markers;
  };

  const handleSubmit = async () => {
    if (!validateStep()) return;

    setConfirmDialogOpen(true);
  };

  const confirmSubmit = async () => {
    try {
      setLoading(true);
      setConfirmDialogOpen(false);

      const orderPayload = {
        customerName: orderData.customerName.trim(),
        customerEmail: orderData.customerEmail.trim().toLowerCase(),
        customerPhone: orderData.customerPhone.replace(/\D/g, ''),
        pickupAddress: orderData.pickupAddress.trim(),
        deliveryAddress: orderData.deliveryAddress.trim(),
        deliveryCoordinates: geocodeResults.delivery || null,
        productName: orderData.productName?.trim() || orderData.packageType,
        quantity: 1,
        packageDetails: orderData.packageDescription || '',
        amount: parseFloat(orderData.amount),
        courierId: orderData.courierId || null,
        priority: orderData.priority,
        notes: orderData.deliveryInstructions || '',
        userId: user.uid,
        packageType: orderData.packageType,
        packageWeight: orderData.packageWeight,
        packageDimensions: orderData.packageDimensions,
        paymentMethod: orderData.paymentMethod,
        estimatedDistance: orderData.estimatedDistance
      };

      const response = await fetch('/api/orders/place', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user?.accessToken}`
        },
        body: JSON.stringify(orderPayload)
      });

      const result = await response.json();

      if (response.ok && result.success) {
        localStorage.removeItem('orderDraft');
        toast.success(`Order created successfully! Order #${result.orderNumber}`);
        
        if (result.orderId) {
          navigate(`/orders/${result.orderId}`);
        } else {
          navigate('/orders');
        }
      } else {
        if (result.details && Array.isArray(result.details)) {
          const errorMessage = result.details.join(', ');
          toast.error(`Validation failed: ${errorMessage}`);
        } else {
          toast.error(result.error || 'Failed to create order');
        }
      }
    } catch (error) {
      console.error('Error creating order:', error);
      try {
        console.warn('API failed, falling back to direct Firestore creation');
        await createOrderDirectly();
      } catch (fallbackError) {
        console.error('Fallback order creation also failed:', fallbackError);
        toast.error(`Failed to create order: ${fallbackError.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const createOrderDirectly = async () => {
    const orderNumber = generateNumericOrderId(10);

    const orderToSubmit = {
      ...orderData,
      orderNumber,
      trackingId: `TRK-${Math.random().toString(36).substring(2, 11).toUpperCase()}`,
      estimatedDeliveryTime: calculateEstimatedDelivery(orderData.priority),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy: user.uid,
      status: orderData.courierId ? 'processing' : 'pending',
      ...(orderData.courierId && {
        assignedCourier: orderData.courierId,
        assignedCourierId: orderData.courierId,
        courierId: orderData.courierId
      })
    };

    const docRef = await addDoc(collection(db, 'orders'), orderToSubmit);

    await addDoc(collection(db, 'orderHistory'), {
      orderId: docRef.id,
      action: 'created',
      timestamp: serverTimestamp(),
      userId: user.uid,
      details: {
        status: orderToSubmit.status,
        note: 'Order created'
      }
    });

    if (orderData.courierId) {
      await addDoc(collection(db, 'notifications'), {
        userId: orderData.courierId,
        type: 'order_assigned',
        title: 'New Order Assigned',
        message: `You have been assigned order #${orderNumber}`,
        read: false,
        createdAt: serverTimestamp()
      });
    }

    localStorage.removeItem('orderDraft');
    toast.success('Order created successfully!');
    navigate('/orders');
  };

  const calculateEstimatedDelivery = (priority) => {
    const now = new Date();
    let estimatedDelivery = new Date(now);

    switch(priority) {
      case 'same-day':
        estimatedDelivery.setHours(now.getHours() + 4);
        break;
      case 'express':
        estimatedDelivery.setDate(now.getDate() + 1);
        break;
      case 'standard':
      default:
        estimatedDelivery.setDate(now.getDate() + 2);
        break;
    }

    return estimatedDelivery.toISOString();
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box display="flex" alignItems="center">
                <Typography variant="h6" gutterBottom>
                  Customer Details
                </Typography>
                <Tooltip title="Enter details of the person sending the package">
                  <IconButton size="small" onClick={() => showHelp('Customer Details', 'Enter the details of the customer who is sending the package. All fields are required and will be used for communication and billing purposes.')}>
                    <Help fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Customer Name"
                name="customerName"
                value={orderData.customerName}
                onChange={handleChange}
                error={!!errors.customerName}
                helperText={errors.customerName}
                required
                InputProps={{
                  startAdornment: <Person sx={{ color: 'action.active', mr: 1 }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
  fullWidth
  label="Customer Email (optional)"
  name="customerEmail"
  type="email"
  value={orderData.customerEmail}
  onChange={handleChange}
  error={!!errors.customerEmail}
  helperText={errors.customerEmail || 'Optional. Enter a valid email to receive notifications.'}
/>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Customer Phone"
                name="customerPhone"
                value={orderData.customerPhone}
                onChange={handleChange}
                error={!!errors.customerPhone}
                helperText={errors.customerPhone}
                required
                placeholder="e.g. +****************"
              />
            </Grid>
          </Grid>
        );
      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box display="flex" alignItems="center">
                <Typography variant="h6" gutterBottom>
                  Package Details
                </Typography>
                <Tooltip title="Enter information about the package">
                  <IconButton size="small" onClick={() => showHelp('Package Details', 'Provide information about the package including type, weight, and dimensions. This information will be used to calculate shipping costs and assign appropriate delivery resources.')}>
                    <Help fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.packageType}>
                <InputLabel>Package Type</InputLabel>
                <Select
                  name="packageType"
                  value={orderData.packageType}
                  onChange={handleChange}
                  label="Package Type"
                  required
                  startAdornment={<Description sx={{ color: 'action.active', mr: 1 }} />}
                >
                  {packageTypes.map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label} (Base: ₹{type.basePrice})
                    </MenuItem>
                  ))}
                </Select>
                {errors.packageType && (
                  <Typography color="error" variant="caption">
                    {errors.packageType}
                  </Typography>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Product Name"
                name="productName"
                value={orderData.productName}
                onChange={handleChange}
                error={!!errors.productName}
                helperText={errors.productName}
                placeholder="e.g. Smartphone, Clothing, Documents"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Package Weight (kg)"
                name="packageWeight"
                type="number"
                inputProps={{ step: "0.1", min: "0.1" }}
                value={orderData.packageWeight}
                onChange={handleChange}
                error={!!errors.packageWeight}
                helperText={errors.packageWeight}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Package Dimensions (L x W x H cm)"
                name="packageDimensions"
                value={orderData.packageDimensions}
                onChange={handleChange}
                placeholder="e.g. 30 x 20 x 15"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Package Description"
                name="packageDescription"
                multiline
                rows={3}
                value={orderData.packageDescription}
                onChange={handleChange}
                placeholder="Describe the contents of the package"
              />
            </Grid>
            {orderData.packageType && orderData.packageWeight && (
              <Grid item xs={12}>
                <Alert severity="info" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    Based on your package type and weight, the estimated base cost will be calculated on the next step.
                  </Typography>
                </Alert>
              </Grid>
            )}
          </Grid>
        );
      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box display="flex" alignItems="center">
                <Typography variant="h6" gutterBottom>
                  Delivery Details
                </Typography>
                <Tooltip title="Specify pickup and delivery information">
                  <IconButton size="small" onClick={() => showHelp('Delivery Details', 'Enter the pickup and delivery addresses, along with any special instructions. The map will show the estimated route and distance between the two locations.')}>
                    <Help fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Pickup Address"
                name="pickupAddress"
                value={orderData.pickupAddress}
                onChange={handleChange}
                error={!!errors.pickupAddress}
                helperText={errors.pickupAddress}
                required
                InputProps={{
                  startAdornment: <LocationOn sx={{ color: 'green', mr: 1 }} />,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Delivery Address"
                name="deliveryAddress"
                value={orderData.deliveryAddress}
                onChange={handleChange}
                error={!!errors.deliveryAddress}
                helperText={errors.deliveryAddress}
                required
                InputProps={{
                  startAdornment: <LocationOn sx={{ color: 'red', mr: 1 }} />,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Delivery Instructions"
                name="deliveryInstructions"
                multiline
                rows={2}
                value={orderData.deliveryInstructions}
                onChange={handleChange}
                placeholder="e.g. Leave with doorman, Call upon arrival, etc."
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  name="priority"
                  value={orderData.priority}
                  onChange={handleChange}
                  label="Priority"
                >
                  {priorityOptions.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label} (x{option.multiplier})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Assign Courier (Optional)</InputLabel>
                <Select
                  name="courierId"
                  value={orderData.courierId}
                  onChange={handleChange}
                  label="Assign Courier (Optional)"
                  disabled={couriers.length === 0}
                >
                  <MenuItem value="">Auto-assign</MenuItem>
                  {couriers.length > 0 ? (
                    couriers.map(courier => (
                      <MenuItem key={courier.id} value={courier.id}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                          <Typography variant="body2" fontWeight="bold">
                            {courier.name || courier.displayName || courier.email || `Courier ${courier.id.substring(0, 5)}`}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                            <Chip
                              size="small"
                              label={courier.status || 'available'}
                              color={courier.status === 'available' ? 'success' : 'warning'}
                              variant="outlined"
                            />
                            <Typography variant="caption" color="text.secondary">
                              {courier.assignedOrdersCount || 0} orders
                            </Typography>
                            {courier.isOnline && (
                              <Chip size="small" label="Online" color="primary" variant="outlined" />
                            )}
                          </Box>
                        </Box>
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem disabled>No couriers available</MenuItem>
                  )}
                </Select>
                {couriers.length === 0 && (
                  <Typography variant="caption" color="error">
                    No couriers available. Please add couriers first.
                  </Typography>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Delivery Route Preview
                {orderData.estimatedDistance && (
                  <Typography component="span" color="primary" sx={{ fontWeight: 'bold', ml: 1 }}>
                    (~{orderData.estimatedDistance} km
                    {orderData.estimatedDuration && `, ${orderData.estimatedDuration} min`}
                    {orderData.routeOptimized && ' ✓ Optimized'}
                    )
                  </Typography>
                )}
              </Typography>
              <Paper sx={{ height: 300, width: '100%', overflow: 'hidden', position: 'relative' }}>
                {loadingMap && (
                  <Box sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'rgba(255,255,255,0.7)',
                    zIndex: 1
                  }}>
                    <CircularProgress />
                  </Box>
                )}
                <EnhancedMap
                  center={mapCenter}
                  zoom={12}
                  markers={getOrderMapMarkers()}
                  showUserLocation={false}
                  enableFullscreen={true}
                  enableRefresh={true}
                  enableMyLocation={false}
                  height={400}
                  onMapLoad={({ map }) => {
                    setMapInstance(map);
                    setMapReady(true);
                    console.log('Google Maps API loaded successfully');
                  }}
                  onMarkerClick={(markerData) => {
                    console.log('Marker clicked:', markerData);
                  }}
                  onRefresh={() => {
                    if (orderData.pickupAddress && orderData.deliveryAddress) {
                      updateMapWithAddresses();
                    }
                  }}
                />
              </Paper>
              {!orderData.pickupAddress || !orderData.deliveryAddress ? (
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Enter both pickup and delivery addresses to see the route
                </Typography>
              ) : null}
            </Grid>
          </Grid>
        );
      case 3:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box display="flex" alignItems="center">
                <Typography variant="h6" gutterBottom>
                  Payment Details
                </Typography>
                <Tooltip title="Specify payment information">
                  <IconButton size="small" onClick={() => showHelp('Payment Details', 'Select a payment method and confirm the calculated amount. For cash on delivery, the courier will collect payment upon delivery.')}>
                    <Help fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Paper sx={{ p: 2, backgroundColor: theme.palette.background.default }}>
                <Typography variant="subtitle1" gutterBottom>
                  Price Calculation
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2">Package Type:</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" fontWeight="bold">
                      {packageTypes.find(t => t.value === orderData.packageType)?.label || ''}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">Weight:</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">{orderData.packageWeight} kg</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">Distance:</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      {orderData.estimatedDistance} km
                      {orderData.estimatedDuration && ` (${orderData.estimatedDuration} min)`}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">Priority:</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    {priorityOptions.find(o => o.value === orderData.priority) && (
                   <Typography variant="body2">
                    {priorityOptions.find(o => o.value === orderData.priority)?.label || ''}
                   </Typography>
                 )}
                  </Grid>
                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2">Estimated Total:</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" fontWeight="bold" color="primary">
                      ₹{orderData.amount || '0.00'}
                    </Typography>
                  </Grid>
                </Grid>

                {!calculatePriceClicked && (
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ mt: 2 }}
                    onClick={calculatePrice}
                  >
                    Recalculate Price
                  </Button>
                )}
              </Paper>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Payment Method</InputLabel>
                <Select
                  name="paymentMethod"
                  value={orderData.paymentMethod}
                  onChange={handleChange}
                  label="Payment Method"
                  startAdornment={<Payment sx={{ color: 'action.active', mr: 1 }} />}
                >
                  {paymentMethods.map(method => (
                    <MenuItem key={method.value} value={method.value}>
                      {method.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Amount"
                name="amount"
                type="number"
                value={orderData.amount}
                onChange={handleChange}
                error={!!errors.amount}
                helperText={errors.amount}
                required
                InputProps={{
                  startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                }}
              />
            </Grid>

            {orderData.paymentMethod === 'card' && (
              <Grid item xs={12}>
                <Alert severity="info">
                  <Typography variant="body2">
                    For credit/debit card payments, the customer will receive a payment link via email upon order confirmation.
                  </Typography>
                </Alert>
              </Grid>
            )}
          </Grid>
        );
      case 4:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6" gutterBottom>
                  Order Summary
                </Typography>
                <Button
                  startIcon={<Save />}
                  onClick={() => saveDraft()}
                  size="small"
                >
                  Save as Draft
                </Button>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Paper sx={{ p: 3, borderRadius: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1" color="text.secondary">
                      Customer
                    </Typography>
                    <Typography variant="body1" gutterBottom fontWeight="medium">
                      {orderData.customerName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {orderData.customerEmail}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {orderData.customerPhone}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1" color="text.secondary">
                      Package
                    </Typography>
                    <Typography variant="body1" gutterBottom fontWeight="medium">
                      {packageTypes.find(t => t.value === orderData.packageType)?.label || ''} ({orderData.packageWeight} kg)
                    </Typography>
                    {orderData.productName && (
                      <Typography variant="body2" color="text.secondary" fontWeight="medium">
                        Product: {orderData.productName}
                      </Typography>
                    )}
                    <Typography variant="body2" color="text.secondary">
                      {orderData.packageDimensions && `Dimensions: ${orderData.packageDimensions}`}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {orderData.packageDescription}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1" color="text.secondary">
                      Pickup Address
                    </Typography>
                    <Typography variant="body1" gutterBottom fontWeight="medium">
                      {orderData.pickupAddress}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1" color="text.secondary">
                      Delivery Address
                    </Typography>
                    <Typography variant="body1" gutterBottom fontWeight="medium">
                      {orderData.deliveryAddress}
                    </Typography>
                    {orderData.deliveryInstructions && (
                      <Typography variant="body2" color="text.secondary">
                        Instructions: {orderData.deliveryInstructions}
                      </Typography>
                    )}
                  </Grid>
                  <Grid item xs={12}>
                    <Box sx={{ my: 2, p: 2, backgroundColor: theme.palette.background.default, borderRadius: 1 }}>
                      <Typography variant="subtitle1" color="text.secondary">
                        Delivery Details
                      </Typography>
                      <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Distance:</Typography>
                          <Typography variant="body1" fontWeight="medium">
                            {orderData.estimatedDistance} km
                            {orderData.estimatedDuration && ` (${orderData.estimatedDuration} min)`}
                            {orderData.routeOptimized && (
                              <Typography component="span" color="success.main" sx={{ ml: 1, fontSize: '0.8em' }}>
                                ✓ Optimized
                              </Typography>
                            )}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Priority:</Typography>
                          <Typography variant="body1" fontWeight="medium" sx={{ textTransform: 'capitalize' }}>
                            {priorityOptions.find(o => o.value === orderData.priority)?.label || ''}
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography variant="body2" color="text.secondary">Estimated Delivery:</Typography>
                          <Typography variant="body1" fontWeight="medium">
                            {orderData.priority === 'same-day' ? 'Today (3-4 hours)' :
                             orderData.priority === 'express' ? 'Tomorrow' :
                             'Within 1-2 business days'}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1" color="text.secondary">
                      Courier Assignment
                    </Typography>
                    <Typography variant="body1" fontWeight="medium">
                      {orderData.courierId
                        ? couriers.find(c => c.id === orderData.courierId)?.displayName || 'Selected Courier'
                        : 'Auto-assignment'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1" color="text.secondary">
                      Payment
                    </Typography>
                    <Typography variant="body1" fontWeight="medium">
                      ₹{orderData.amount} ({paymentMethods.find(m => m.value === orderData.paymentMethod)?.label || ''})
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>
            <Grid item xs={12}>
              <Alert severity="info" icon={<Info />}>
                Please review all information carefully before submitting. Once created, the order will be processed immediately.
              </Alert>
            </Grid>
          </Grid>
        );
      default:
        return null;
    }
  };

  return (
    <AdminLayout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
          <Paper
            elevation={0}
            sx={{
              p: { xs: 2, sm: 4 },
              borderRadius: 4,
              boxShadow: '0 0 10px rgba(0,0,0,0.1)',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <IconButton
                sx={{ mr: 2 }}
                onClick={() => navigate('/orders')}
                aria-label="back to orders"
              >
                <ArrowBack />
              </IconButton>
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                Create New Order
              </Typography>
            </Box>

            <Stepper
              activeStep={activeStep}
              sx={{
                mb: 4,
                display: { xs: 'none', sm: 'flex' }
              }}
            >
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>

            {/* Mobile stepper alternative */}
            <Box sx={{ display: { xs: 'block', sm: 'none' }, mb: 3 }}>
              <Typography variant="subtitle1" textAlign="center">
                Step {activeStep + 1}: {steps[activeStep]}
              </Typography>
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mt: 1
              }}>
                {steps.map((_, index) => (
                  <Box
                    key={index}
                    sx={{
                      height: 6,
                      width: `calc(${100 / steps.length}% - 4px)`,
                      backgroundColor: index <= activeStep ? theme.palette.primary.main : theme.palette.grey[300],
                      borderRadius: 3
                    }}
                  />
                ))}
              </Box>
            </Box>

            {renderStepContent(activeStep)}

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
                startIcon={<ArrowBack />}
                sx={{ mr: 1 }}
              >
                Back
              </Button>
              <Box>
                {activeStep !== steps.length - 1 && (
                  <Button
                    variant="outlined"
                    onClick={saveDraft}
                    sx={{ mr: 2 }}
                  >
                    Save Draft
                  </Button>
                )}
                <Button
                  variant="contained"
                  color="primary"
                  onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}
                  disabled={loading}
                  sx={{ borderRadius: 2, px: 4 }}
                  endIcon={activeStep === steps.length - 1 ? <Check /> : null}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : activeStep === steps.length - 1 ? (
                    'Create Order'
                  ) : (
                    'Next'
                  )}
                </Button>
              </Box>
            </Box>
          </Paper>
        </Container>

        {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
      >
        <DialogTitle>Confirm Order Creation</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to create this order? This will initiate the delivery process and notify the assigned courier if selected.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={confirmSubmit} color="primary" variant="contained" autoFocus>
            {loading ? <CircularProgress size={24} /> : 'Confirm'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Draft Dialog */}
      <Dialog
        open={saveDraftDialogOpen}
        onClose={() => setSaveDraftDialogOpen(false)}
      >
        <DialogTitle>Load Saved Draft?</DialogTitle>
        <DialogContent>
          <DialogContentText>
            We found a previously saved draft for an order. Would you like to continue with this draft or start a new order?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={clearDraft} color="error">
            Start New
          </Button>
          <Button onClick={loadDraft} color="primary" variant="contained" autoFocus>
            Load Draft
          </Button>
        </DialogActions>
      </Dialog>

      {/* Help Dialog */}
      <Dialog
        open={helpDialogOpen}
        onClose={() => setHelpDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{helpContent.title}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {helpContent.content}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setHelpDialogOpen(false)} color="primary">
            Got it
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, open: false })}
        message={notification.message}
      />
    </AdminLayout>
  );
};

export default CreateOrder;