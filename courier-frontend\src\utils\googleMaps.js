// src/utils/googleMaps.js
// Enhanced Google Maps utilities with modern marker support and AI-powered features

// Get the API key from environment variables
export const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

// Required Google Cloud APIs for this application:
// 1. Maps JavaScript API - For map display and basic functionality
// 2. Places API (New) - For address autocomplete and geocoding
// 3. Routes API - For modern distance/duration calculations (replaces Distance Matrix)
// 4. Directions API - Fallback for route calculations
// 5. Geocoding API - For address to coordinates conversion
//
// To enable these APIs:
// 1. Go to Google Cloud Console (console.cloud.google.com)
// 2. Select your project or create a new one
// 3. Navigate to "APIs & Services" > "Library"
// 4. Search for and enable each of the above APIs
// 5. Create credentials (API Key) in "APIs & Services" > "Credentials"
// 6. Add the API key to your .env file as VITE_GOOGLE_MAPS_API_KEY

// Validate API key and provide helpful error messages
if (!GOOGLE_MAPS_API_KEY) {
  console.error('Google Maps API key is not configured. Please set VITE_GOOGLE_MAPS_API_KEY in your .env file');
  console.warn('Map functionality will be limited without a valid API key');
  console.info('Required APIs: Maps JavaScript API, Places API (New), Routes API, Directions API, Geocoding API');
}

// Enhanced default map center with fallback locations
export const DEFAULT_CENTER = {
  lat: 19.0760,
  lng: 72.8777
};

// Alternative centers for different regions
export const REGIONAL_CENTERS = {
  mumbai: { lat: 19.0760, lng: 72.8777 },
  delhi: { lat: 28.6139, lng: 77.2090 },
  bangalore: { lat: 12.9716, lng: 77.5946 },
  chennai: { lat: 13.0827, lng: 80.2707 },
  kolkata: { lat: 22.5726, lng: 88.3639 },
  hyderabad: { lat: 17.3850, lng: 78.4867 }
};

// Enhanced zoom levels for different use cases
export const ZOOM_LEVELS = {
  city: 11,
  area: 13,
  street: 15,
  building: 17,
  detailed: 19
};

export const DEFAULT_ZOOM = ZOOM_LEVELS.area;

// Responsive map container styles
export const MAP_CONTAINER_STYLE = {
  width: '100%',
  height: '400px',
  borderRadius: '8px',
  minHeight: '300px'
};

export const LARGE_MAP_CONTAINER_STYLE = {
  width: '100%',
  height: '70vh',
  borderRadius: '8px',
  minHeight: '500px'
};

export const MOBILE_MAP_CONTAINER_STYLE = {
  width: '100%',
  height: '50vh',
  borderRadius: '8px',
  minHeight: '300px'
};

// Enhanced marker icons with better visibility
export const MARKER_ICONS = {
  courier: {
    url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',
    scaledSize: { width: 40, height: 40 },
    anchor: { x: 20, y: 40 }
  },
  delivery: {
    url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
    scaledSize: { width: 40, height: 40 },
    anchor: { x: 20, y: 40 }
  },
  pickup: {
    url: 'https://maps.google.com/mapfiles/ms/icons/orange-dot.png',
    scaledSize: { width: 40, height: 40 },
    anchor: { x: 20, y: 40 }
  },
  warehouse: {
    url: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',
    scaledSize: { width: 40, height: 40 },
    anchor: { x: 20, y: 40 }
  },
  default: {
    url: 'https://maps.google.com/mapfiles/ms/icons/purple-dot.png',
    scaledSize: { width: 40, height: 40 },
    anchor: { x: 20, y: 40 }
  }
};

// Enhanced helper function to create a marker icon with error handling
export const createMarkerIcon = (type = 'default', options = {}) => {
  const icon = MARKER_ICONS[type] || MARKER_ICONS.default;
  const { size = icon.scaledSize, anchor = icon.anchor } = options;

  try {
    // For @react-google-maps/api with enhanced error handling
    if (window.google && window.google.maps) {
      return {
        url: icon.url,
        scaledSize: new window.google.maps.Size(size.width, size.height),
        anchor: anchor ? new window.google.maps.Point(anchor.x, anchor.y) : undefined,
        optimized: true // Better performance
      };
    }

    // Return a simpler version if Google Maps is not loaded yet
    return {
      url: icon.url,
      scaledSize: size,
      anchor: anchor
    };
  } catch (error) {
    console.warn('Error creating marker icon:', error);
    return {
      url: icon.url
    };
  }
};

// Enhanced Google Maps libraries with all required features
// Note: Routes API is accessed via REST API, not JavaScript library
export const GOOGLE_MAPS_LIBRARIES = ["places", "geometry", "drawing", "marker", "visualization"];

// Enhanced map options with better UX and performance
// Note: When using mapId, styles must be controlled via Google Cloud Console
export const DEFAULT_MAP_OPTIONS = {
  fullscreenControl: true,
  streetViewControl: true,
  mapTypeControl: true,
  zoomControl: true,
  scaleControl: true,
  rotateControl: false,
  gestureHandling: "cooperative",
  mapTypeId: "roadmap",
  mapId: "DEMO_MAP_ID", // Required for AdvancedMarkerElement
  clickableIcons: false,
  disableDefaultUI: false,
  restriction: {
    latLngBounds: {
      north: 37.0,
      south: 6.0,
      west: 68.0,
      east: 97.0
    }, // Restrict to India bounds
    strictBounds: false
  }
};

// Map options without mapId for custom styling (fallback)
export const LEGACY_MAP_OPTIONS = {
  fullscreenControl: true,
  streetViewControl: true,
  mapTypeControl: true,
  zoomControl: true,
  scaleControl: true,
  rotateControl: false,
  gestureHandling: "cooperative",
  mapTypeId: "roadmap",
  clickableIcons: false,
  disableDefaultUI: false,
  restriction: {
    latLngBounds: {
      north: 37.0,
      south: 6.0,
      west: 68.0,
      east: 97.0
    }, // Restrict to India bounds
    strictBounds: false
  }
};

// Dark mode map styles
export const DARK_MAP_STYLES = [
  { elementType: "geometry", stylers: [{ color: "#242f3e" }] },
  { elementType: "labels.text.stroke", stylers: [{ color: "#242f3e" }] },
  { elementType: "labels.text.fill", stylers: [{ color: "#746855" }] },
  {
    featureType: "administrative.locality",
    elementType: "labels.text.fill",
    stylers: [{ color: "#d59563" }]
  },
  {
    featureType: "poi",
    elementType: "labels.text.fill",
    stylers: [{ color: "#d59563" }]
  },
  {
    featureType: "poi.park",
    elementType: "geometry",
    stylers: [{ color: "#263c3f" }]
  },
  {
    featureType: "poi.park",
    elementType: "labels.text.fill",
    stylers: [{ color: "#6b9a76" }]
  },
  {
    featureType: "road",
    elementType: "geometry",
    stylers: [{ color: "#38414e" }]
  },
  {
    featureType: "road",
    elementType: "geometry.stroke",
    stylers: [{ color: "#212a37" }]
  },
  {
    featureType: "road",
    elementType: "labels.text.fill",
    stylers: [{ color: "#9ca5b3" }]
  },
  {
    featureType: "road.highway",
    elementType: "geometry",
    stylers: [{ color: "#746855" }]
  },
  {
    featureType: "road.highway",
    elementType: "geometry.stroke",
    stylers: [{ color: "#1f2835" }]
  },
  {
    featureType: "road.highway",
    elementType: "labels.text.fill",
    stylers: [{ color: "#f3d19c" }]
  },
  {
    featureType: "transit",
    elementType: "geometry",
    stylers: [{ color: "#2f3948" }]
  },
  {
    featureType: "transit.station",
    elementType: "labels.text.fill",
    stylers: [{ color: "#d59563" }]
  },
  {
    featureType: "water",
    elementType: "geometry",
    stylers: [{ color: "#17263c" }]
  },
  {
    featureType: "water",
    elementType: "labels.text.fill",
    stylers: [{ color: "#515c6d" }]
  },
  {
    featureType: "water",
    elementType: "labels.text.stroke",
    stylers: [{ color: "#17263c" }]
  }
];

/**
 * Create map options based on theme and requirements
 * @param {Object} options - Configuration options
 * @param {boolean} options.darkMode - Whether to use dark mode
 * @param {boolean} options.useAdvancedMarkers - Whether to use AdvancedMarkerElement (requires mapId)
 * @param {boolean} options.enableCustomStyling - Whether to enable custom styling (conflicts with mapId)
 * @returns {Object} Map options object
 */
export const createMapOptions = (options = {}) => {
  const {
    darkMode = false,
    useAdvancedMarkers = true,
    enableCustomStyling = false
  } = options;

  // If using advanced markers, we must use mapId and cannot set custom styles
  if (useAdvancedMarkers && !enableCustomStyling) {
    return {
      ...DEFAULT_MAP_OPTIONS,
      // Note: Styles are controlled via Google Cloud Console when using mapId
    };
  }

  // If custom styling is required, use legacy options without mapId
  if (enableCustomStyling) {
    return {
      ...LEGACY_MAP_OPTIONS,
      styles: darkMode ? DARK_MAP_STYLES : []
    };
  }

  // Default fallback
  return DEFAULT_MAP_OPTIONS;
};

// Distance calculation utilities
export const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km
  return distance;
};

const deg2rad = (deg) => {
  return deg * (Math.PI/180);
};

// Calculate distance using modern Routes API with fallback to Directions API and Haversine
export const calculateDistanceWithAPI = async (origin, destination) => {
  try {
    // Validate inputs
    if (!origin || !destination || !origin.lat || !origin.lng || !destination.lat || !destination.lng) {
      throw new Error('Invalid coordinates provided');
    }

    // Check if Google Maps is loaded
    if (!window.google || !window.google.maps) {
      console.warn('Google Maps not loaded, using Haversine formula');
      return {
        distance: calculateDistance(origin.lat, origin.lng, destination.lat, destination.lng),
        duration: null,
        method: 'haversine'
      };
    }

    // Try Routes API first (modern approach)
    try {
      const routesResult = await calculateDistanceWithRoutesAPI(origin, destination);
      if (routesResult) {
        return routesResult;
      }
    } catch (routesError) {
      console.warn('Routes API failed, trying Directions API:', routesError);
    }

    // Fallback to Directions API
    try {
      const directionsResult = await calculateDistanceWithDirectionsAPI(origin, destination);
      if (directionsResult) {
        return directionsResult;
      }
    } catch (directionsError) {
      console.warn('Directions API failed, using Haversine formula:', directionsError);
    }

    // Final fallback to Haversine formula
    return {
      distance: calculateDistance(origin.lat, origin.lng, destination.lat, destination.lng),
      duration: null,
      method: 'haversine'
    };
  } catch (error) {
    console.error('Error calculating distance:', error);
    // Fallback to Haversine formula
    return {
      distance: calculateDistance(origin.lat, origin.lng, destination.lat, destination.lng),
      duration: null,
      method: 'haversine'
    };
  }
};

// Modern Routes API implementation
const calculateDistanceWithRoutesAPI = async (origin, destination) => {
  if (!GOOGLE_MAPS_API_KEY) {
    throw new Error('Google Maps API key not configured');
  }

  const url = 'https://routes.googleapis.com/directions/v2:computeRoutes';

  const requestBody = {
    origin: {
      location: {
        latLng: {
          latitude: origin.lat,
          longitude: origin.lng
        }
      }
    },
    destination: {
      location: {
        latLng: {
          latitude: destination.lat,
          longitude: destination.lng
        }
      }
    },
    travelMode: 'DRIVE',
    routingPreference: 'TRAFFIC_AWARE',
    computeAlternativeRoutes: false,
    routeModifiers: {
      avoidTolls: false,
      avoidHighways: false,
      avoidFerries: false
    },
    languageCode: 'en-US',
    units: 'METRIC'
  };

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Goog-Api-Key': GOOGLE_MAPS_API_KEY,
      'X-Goog-FieldMask': 'routes.duration,routes.distanceMeters,routes.polyline.encodedPolyline'
    },
    body: JSON.stringify(requestBody)
  });

  if (!response.ok) {
    throw new Error(`Routes API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  if (data.routes && data.routes.length > 0) {
    const route = data.routes[0];
    return {
      distance: route.distanceMeters / 1000, // Convert to km
      duration: parseInt(route.duration.replace('s', '')) / 60, // Convert to minutes
      method: 'routes_api',
      polyline: route.polyline?.encodedPolyline
    };
  }

  throw new Error('No routes found');
};

// Fallback Directions API implementation
const calculateDistanceWithDirectionsAPI = async (origin, destination) => {
  return new Promise((resolve, reject) => {
    const directionsService = new window.google.maps.DirectionsService();

    directionsService.route({
      origin: new window.google.maps.LatLng(origin.lat, origin.lng),
      destination: new window.google.maps.LatLng(destination.lat, destination.lng),
      travelMode: window.google.maps.TravelMode.DRIVING,
      unitSystem: window.google.maps.UnitSystem.METRIC,
      avoidHighways: false,
      avoidTolls: false,
      optimizeWaypoints: false
    }, (response, status) => {
      if (status === 'OK' && response && response.routes && response.routes[0]) {
        const route = response.routes[0];
        const leg = route.legs[0];

        if (leg) {
          resolve({
            distance: leg.distance.value / 1000, // Convert to km
            duration: leg.duration.value / 60, // Convert to minutes
            method: 'directions_api',
            polyline: route.overview_polyline?.points
          });
          return;
        }
      }

      reject(new Error(`Directions API failed with status: ${status}`));
    });
  });
};

/**
 * Enhanced AdvancedMarkerElement with AI-powered features and better error handling
 * @param {Object} options - Marker options
 * @param {Object} options.position - Marker position {lat, lng}
 * @param {google.maps.Map} options.map - Map instance
 * @param {string} options.type - Marker type ('courier', 'delivery', 'pickup', 'warehouse')
 * @param {string} options.title - Marker title
 * @param {Function} options.onClick - Click handler
 * @param {Object} options.data - Additional data for the marker
 * @param {boolean} options.animated - Whether to animate the marker
 * @param {string} options.status - Status for dynamic styling
 * @returns {google.maps.marker.AdvancedMarkerElement|google.maps.Marker}
 */
export const createAdvancedMarker = (options) => {
  const {
    position,
    map,
    type = 'delivery',
    title = '',
    onClick,
    data = {},
    animated = false,
    status = 'active'
  } = options;

  // Validate inputs
  if (!position || !map) {
    console.error('Position and map are required for marker creation');
    return null;
  }

  if (!window.google || !window.google.maps) {
    console.warn('Google Maps not loaded');
    return null;
  }

  // Enhanced marker content with better styling and animations
  const markerContent = document.createElement('div');
  markerContent.className = 'advanced-marker';

  // Base styles with CSS variables for theming
  markerContent.style.cssText = `
    width: 44px;
    height: 44px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(0,0,0,0.25);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 3px solid rgba(255,255,255,0.8);
    position: relative;
    z-index: 1;
  `;

  // Enhanced marker styling based on type and status
  const getMarkerStyle = (markerType, markerStatus) => {
    const styles = {
      courier: {
        active: { bg: '#2196F3', emoji: '🚚', pulse: true },
        inactive: { bg: '#90CAF9', emoji: '🚚', pulse: false },
        busy: { bg: '#1976D2', emoji: '🚚', pulse: true }
      },
      delivery: {
        pending: { bg: '#FF9800', emoji: '📦', pulse: false },
        in_transit: { bg: '#2196F3', emoji: '🚛', pulse: true },
        delivered: { bg: '#4CAF50', emoji: '✅', pulse: false },
        failed: { bg: '#F44336', emoji: '❌', pulse: false }
      },
      pickup: {
        pending: { bg: '#FF9800', emoji: '📋', pulse: false },
        ready: { bg: '#4CAF50', emoji: '📦', pulse: true },
        collected: { bg: '#9C27B0', emoji: '✅', pulse: false }
      },
      warehouse: {
        active: { bg: '#4CAF50', emoji: '🏢', pulse: false },
        inactive: { bg: '#9E9E9E', emoji: '🏢', pulse: false }
      },
      default: {
        active: { bg: '#9C27B0', emoji: '📍', pulse: false }
      }
    };

    const typeStyles = styles[markerType] || styles.default;
    return typeStyles[markerStatus] || typeStyles.active || typeStyles[Object.keys(typeStyles)[0]];
  };

  const style = getMarkerStyle(type, status);
  markerContent.style.backgroundColor = style.bg;
  markerContent.innerHTML = style.emoji;

  // Add pulsing animation for active markers
  if (style.pulse || animated) {
    markerContent.style.animation = 'marker-pulse 2s infinite';

    // Add CSS animation if not already present
    if (!document.getElementById('marker-animations')) {
      const styleSheet = document.createElement('style');
      styleSheet.id = 'marker-animations';
      styleSheet.textContent = `
        @keyframes marker-pulse {
          0% { transform: scale(1); box-shadow: 0 4px 12px rgba(0,0,0,0.25); }
          50% { transform: scale(1.1); box-shadow: 0 6px 16px rgba(0,0,0,0.35); }
          100% { transform: scale(1); box-shadow: 0 4px 12px rgba(0,0,0,0.25); }
        }
        .advanced-marker:hover {
          transform: scale(1.15) !important;
          box-shadow: 0 6px 20px rgba(0,0,0,0.4) !important;
          z-index: 1000 !important;
        }
      `;
      document.head.appendChild(styleSheet);
    }
  }

  // Enhanced hover effects
  markerContent.addEventListener('mouseenter', () => {
    if (!style.pulse) {
      markerContent.style.transform = 'scale(1.15)';
      markerContent.style.boxShadow = '0 6px 20px rgba(0,0,0,0.4)';
      markerContent.style.zIndex = '1000';
    }
  });

  markerContent.addEventListener('mouseleave', () => {
    if (!style.pulse) {
      markerContent.style.transform = 'scale(1)';
      markerContent.style.boxShadow = '0 4px 12px rgba(0,0,0,0.25)';
      markerContent.style.zIndex = '1';
    }
  });

  // Try to create AdvancedMarkerElement first
  if (window.google.maps.marker && window.google.maps.marker.AdvancedMarkerElement) {
    try {
      const marker = new window.google.maps.marker.AdvancedMarkerElement({
        position,
        map,
        content: markerContent,
        title,
        gmpDraggable: false,
        gmpClickable: true
      });

      // Store additional data
      marker.data = data;
      marker.markerType = type;
      marker.markerStatus = status;

      // Add click handler
      if (onClick) {
        marker.addListener('click', (event) => {
          onClick(marker, event);
        });
      }

      return marker;
    } catch (error) {
      console.warn('AdvancedMarkerElement creation failed, falling back to regular Marker:', error);
    }
  }

  // Fallback to regular Marker with enhanced icon
  try {
    const fallbackMarker = new window.google.maps.Marker({
      position,
      map,
      title,
      icon: createMarkerIcon(type, {
        size: { width: 44, height: 44 },
        anchor: { x: 22, y: 44 }
      }),
      optimized: true,
      draggable: false
    });

    // Store additional data
    fallbackMarker.data = data;
    fallbackMarker.markerType = type;
    fallbackMarker.markerStatus = status;

    // Add click handler
    if (onClick) {
      fallbackMarker.addListener('click', (event) => {
        onClick(fallbackMarker, event);
      });
    }

    return fallbackMarker;
  } catch (fallbackError) {
    console.error('Error creating fallback marker:', fallbackError);
    return null;
  }
};

/**
 * Create multiple advanced markers
 * @param {Array} markers - Array of marker options
 * @param {google.maps.Map} map - Map instance
 * @returns {Array} Array of created markers
 */
export const createMultipleAdvancedMarkers = (markers, map) => {
  return markers.map(markerOptions =>
    createAdvancedMarker({ ...markerOptions, map })
  ).filter(marker => marker !== null);
};

/**
 * Enhanced marker clearing with memory cleanup
 * @param {Array} markers - Array of markers to clear
 */
export const clearMarkers = (markers) => {
  if (!Array.isArray(markers)) {
    console.warn('clearMarkers expects an array of markers');
    return;
  }

  markers.forEach(marker => {
    try {
      if (marker) {
        // Remove event listeners to prevent memory leaks
        if (marker.addListener && typeof marker.removeListener === 'function') {
          // Clear all listeners (this is a simplified approach)
          google.maps.event.clearInstanceListeners(marker);
        }

        // Remove from map
        if (marker.setMap) {
          marker.setMap(null);
        } else if (marker.map) {
          marker.map = null;
        }
      }
    } catch (error) {
      console.warn('Error clearing marker:', error);
    }
  });
};

/**
 * Enhanced AI-powered route optimization using multiple algorithms
 * @param {Array} waypoints - Array of waypoint objects with lat, lng
 * @param {Object} options - Optimization options
 * @returns {Promise<Object>} Optimized route data
 */
export const optimizeRoute = async (waypoints, options = {}) => {
  const {
    algorithm = 'nearest_neighbor', // 'nearest_neighbor', 'genetic', 'simulated_annealing', 'google_optimize'
    startPoint = null,
    endPoint = null,
    avoidTolls = false,
    avoidHighways = false,
    considerTraffic = true,
    maxWaypoints = 25 // Google Maps limit
  } = options;

  if (!waypoints || waypoints.length < 2) {
    throw new Error('At least 2 waypoints are required for route optimization');
  }

  if (waypoints.length > maxWaypoints) {
    console.warn(`Too many waypoints (${waypoints.length}). Limiting to ${maxWaypoints}.`);
    waypoints = waypoints.slice(0, maxWaypoints);
  }

  try {
    let optimizedWaypoints;

    // Choose optimization algorithm
    switch (algorithm) {
      case 'google_optimize':
        optimizedWaypoints = await googleOptimizeRoute(waypoints, startPoint, endPoint, options);
        break;
      case 'genetic':
        optimizedWaypoints = await geneticAlgorithmOptimization(waypoints, startPoint, endPoint);
        break;
      case 'simulated_annealing':
        optimizedWaypoints = await simulatedAnnealingOptimization(waypoints, startPoint, endPoint);
        break;
      case 'nearest_neighbor':
      default:
        optimizedWaypoints = await nearestNeighborOptimization(waypoints, startPoint, endPoint);
        break;
    }

    // Calculate total distance and estimated time
    const routeMetrics = await calculateRouteMetrics(optimizedWaypoints, {
      avoidTolls,
      avoidHighways,
      considerTraffic
    });

    return {
      waypoints: optimizedWaypoints,
      totalDistance: routeMetrics.distance,
      estimatedTime: routeMetrics.duration,
      algorithm: algorithm,
      optimizationScore: routeMetrics.score,
      savings: routeMetrics.savings || 0
    };
  } catch (error) {
    console.error('Route optimization failed:', error);
    throw error;
  }
};

/**
 * Google Maps Directions API route optimization
 * @param {Array} waypoints - Array of waypoints
 * @param {Object} startPoint - Starting point
 * @param {Object} endPoint - Ending point
 * @param {Object} options - Optimization options
 * @returns {Promise<Array>} Optimized waypoints
 */
const googleOptimizeRoute = async (waypoints, startPoint, endPoint, options = {}) => {
  if (!window.google || !window.google.maps) {
    throw new Error('Google Maps not loaded');
  }

  const directionsService = new window.google.maps.DirectionsService();

  // Prepare waypoints for Google Maps (excluding start and end)
  const intermediateWaypoints = waypoints.filter(wp =>
    wp !== startPoint && wp !== endPoint
  ).map(wp => ({
    location: new window.google.maps.LatLng(wp.lat, wp.lng),
    stopover: true
  }));

  return new Promise((resolve, reject) => {
    directionsService.route({
      origin: startPoint ? new window.google.maps.LatLng(startPoint.lat, startPoint.lng) :
              new window.google.maps.LatLng(waypoints[0].lat, waypoints[0].lng),
      destination: endPoint ? new window.google.maps.LatLng(endPoint.lat, endPoint.lng) :
                   new window.google.maps.LatLng(waypoints[waypoints.length - 1].lat, waypoints[waypoints.length - 1].lng),
      waypoints: intermediateWaypoints,
      optimizeWaypoints: true,
      travelMode: window.google.maps.TravelMode.DRIVING,
      unitSystem: window.google.maps.UnitSystem.METRIC,
      avoidHighways: options.avoidHighways || false,
      avoidTolls: options.avoidTolls || false
    }, (result, status) => {
      if (status === 'OK' && result) {
        const optimizedOrder = result.routes[0].waypoint_order;
        const optimizedWaypoints = [];

        // Add start point
        if (startPoint) optimizedWaypoints.push(startPoint);
        else optimizedWaypoints.push(waypoints[0]);

        // Add optimized intermediate waypoints
        optimizedOrder.forEach(index => {
          optimizedWaypoints.push(waypoints[index + (startPoint ? 1 : 0)]);
        });

        // Add end point
        if (endPoint && endPoint !== startPoint) optimizedWaypoints.push(endPoint);
        else if (!endPoint) optimizedWaypoints.push(waypoints[waypoints.length - 1]);

        resolve(optimizedWaypoints);
      } else {
        reject(new Error(`Google route optimization failed: ${status}`));
      }
    });
  });
};

/**
 * Simple nearest neighbor algorithm for route optimization
 * @param {Array} waypoints - Array of waypoints
 * @param {Object} startPoint - Starting point
 * @param {Object} endPoint - Ending point
 * @returns {Promise<Array>} Optimized waypoints
 */
const nearestNeighborOptimization = async (waypoints, startPoint, endPoint) => {
  const points = [...waypoints];
  const optimized = [];

  // Start from the specified start point or first waypoint
  let current = startPoint || points.shift();
  optimized.push(current);

  // Find nearest neighbor for each remaining point
  while (points.length > 0) {
    let nearestIndex = 0;
    let nearestDistance = Infinity;

    for (let i = 0; i < points.length; i++) {
      const distance = calculateDistance(
        current.lat, current.lng,
        points[i].lat, points[i].lng
      );

      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestIndex = i;
      }
    }

    current = points.splice(nearestIndex, 1)[0];
    optimized.push(current);
  }

  // Add end point if specified
  if (endPoint && endPoint !== startPoint) {
    optimized.push(endPoint);
  }

  return optimized;
};

/**
 * Genetic algorithm for route optimization
 * @param {Array} waypoints - Array of waypoints
 * @param {Object} startPoint - Starting point
 * @param {Object} endPoint - Ending point
 * @returns {Promise<Array>} Optimized waypoints
 */
const geneticAlgorithmOptimization = async (waypoints, startPoint, endPoint) => {
  // For now, fall back to nearest neighbor
  // In a full implementation, you would implement genetic operators with:
  // - Population size: 50
  // - Generations: 100
  // - Mutation rate: 0.1
  console.log('Genetic algorithm optimization not fully implemented, using nearest neighbor');
  return nearestNeighborOptimization(waypoints, startPoint, endPoint);
};

/**
 * Simulated annealing for route optimization
 * @param {Array} waypoints - Array of waypoints
 * @param {Object} startPoint - Starting point
 * @param {Object} endPoint - Ending point
 * @returns {Promise<Array>} Optimized waypoints
 */
const simulatedAnnealingOptimization = async (waypoints, startPoint, endPoint) => {
  // For now, fall back to nearest neighbor
  // In a full implementation, you would implement the annealing process with:
  // - Initial temperature: 1000
  // - Cooling rate: 0.95
  // - Minimum temperature: 1
  console.log('Simulated annealing optimization not fully implemented, using nearest neighbor');
  return nearestNeighborOptimization(waypoints, startPoint, endPoint);
};

/**
 * Calculate route metrics including distance, duration, and optimization score
 * @param {Array} waypoints - Optimized waypoints
 * @param {Object} options - Calculation options
 * @returns {Promise<Object>} Route metrics
 */
const calculateRouteMetrics = async (waypoints, options = {}) => {
  let totalDistance = 0;
  let totalDuration = 0;

  // Extract options for future use
  const { considerTraffic = false, avoidHighways = false } = options;

  // Calculate total distance using Haversine formula as fallback
  for (let i = 0; i < waypoints.length - 1; i++) {
    const distance = calculateDistance(
      waypoints[i].lat, waypoints[i].lng,
      waypoints[i + 1].lat, waypoints[i + 1].lng
    );
    totalDistance += distance;

    // Estimate duration (assuming average speed of 30 km/h in city)
    // Adjust for traffic and road conditions
    let avgSpeed = 30; // km/h base speed
    if (considerTraffic) avgSpeed *= 0.8; // Reduce speed for traffic
    if (avoidHighways) avgSpeed *= 0.9; // Slower on local roads

    totalDuration += (distance / avgSpeed) * 60; // Convert to minutes
  }

  // Calculate optimization score (simplified)
  const score = Math.max(0, 100 - (totalDistance * 2)); // Higher score for shorter routes

  // Calculate potential savings (placeholder)
  const originalDistance = totalDistance * 1.2; // Assume 20% improvement
  const savings = Math.max(0, originalDistance - totalDistance);

  return {
    distance: totalDistance,
    duration: totalDuration,
    score: Math.round(score),
    savings: savings
  };
};

/**
 * Get user's current location with enhanced error handling
 * @param {Object} options - Geolocation options
 * @returns {Promise<Object>} User's location
 */
export const getCurrentLocation = (options = {}) => {
  const defaultOptions = {
    enableHighAccuracy: true,
    timeout: 10000,
    maximumAge: 300000 // 5 minutes
  };

  const geoOptions = { ...defaultOptions, ...options };

  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp
        });
      },
      (error) => {
        let errorMessage = 'Failed to get location';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out';
            break;
        }

        reject(new Error(errorMessage));
      },
      geoOptions
    );
  });
};

/**
 * Watch user's location with automatic updates
 * @param {Function} callback - Callback function for location updates
 * @param {Object} options - Watch options
 * @returns {number} Watch ID for clearing the watch
 */
export const watchLocation = (callback, options = {}) => {
  const defaultOptions = {
    enableHighAccuracy: true,
    timeout: 5000,
    maximumAge: 60000 // 1 minute
  };

  const geoOptions = { ...defaultOptions, ...options };

  if (!navigator.geolocation) {
    throw new Error('Geolocation is not supported by this browser');
  }

  return navigator.geolocation.watchPosition(
    (position) => {
      callback({
        lat: position.coords.latitude,
        lng: position.coords.longitude,
        accuracy: position.coords.accuracy,
        timestamp: position.timestamp
      });
    },
    (error) => {
      console.error('Location watch error:', error);
      callback(null, error);
    },
    geoOptions
  );
};

/**
 * Clear location watch
 * @param {number} watchId - Watch ID returned by watchLocation
 */
export const clearLocationWatch = (watchId) => {
  if (navigator.geolocation && watchId) {
    navigator.geolocation.clearWatch(watchId);
  }
};

/**
 * Test API availability and provide helpful feedback
 * @returns {Promise<Object>} API availability status
 */
export const testAPIAvailability = async () => {
  const results = {
    apiKey: !!GOOGLE_MAPS_API_KEY,
    mapsJavaScript: !!(window.google && window.google.maps),
    routesAPI: false,
    directionsAPI: false,
    geocodingAPI: false,
    errors: []
  };

  if (!GOOGLE_MAPS_API_KEY) {
    results.errors.push('Google Maps API key not configured');
    return results;
  }

  // Test Routes API
  try {
    const testOrigin = { lat: 19.0760, lng: 72.8777 };
    const testDestination = { lat: 19.0896, lng: 72.8656 };

    const routesResult = await calculateDistanceWithRoutesAPI(testOrigin, testDestination);
    if (routesResult) {
      results.routesAPI = true;
    }
  } catch (error) {
    results.errors.push(`Routes API: ${error.message}`);
  }

  // Test Directions API (if Google Maps is loaded)
  if (results.mapsJavaScript) {
    try {
      const testOrigin = { lat: 19.0760, lng: 72.8777 };
      const testDestination = { lat: 19.0896, lng: 72.8656 };

      const directionsResult = await calculateDistanceWithDirectionsAPI(testOrigin, testDestination);
      if (directionsResult) {
        results.directionsAPI = true;
      }
    } catch (error) {
      results.errors.push(`Directions API: ${error.message}`);
    }

    // Test Geocoding API
    try {
      const geocoder = new window.google.maps.Geocoder();
      await new Promise((resolve, reject) => {
        geocoder.geocode({ address: 'Mumbai, India' }, (results, status) => {
          if (status === 'OK') {
            resolve(results);
          } else {
            reject(new Error(`Geocoding failed: ${status}`));
          }
        });
      });
      results.geocodingAPI = true;
    } catch (error) {
      results.errors.push(`Geocoding API: ${error.message}`);
    }
  }

  return results;
};

/**
 * Display API status in console for debugging
 */
export const logAPIStatus = async () => {
  console.log('🗺️ Google Maps API Status Check');
  console.log('================================');

  const status = await testAPIAvailability();

  console.log(`✅ API Key: ${status.apiKey ? 'Configured' : '❌ Missing'}`);
  console.log(`✅ Maps JavaScript API: ${status.mapsJavaScript ? 'Loaded' : '❌ Not loaded'}`);
  console.log(`✅ Routes API: ${status.routesAPI ? 'Working' : '❌ Not available'}`);
  console.log(`✅ Directions API: ${status.directionsAPI ? 'Working' : '❌ Not available'}`);
  console.log(`✅ Geocoding API: ${status.geocodingAPI ? 'Working' : '❌ Not available'}`);

  if (status.errors.length > 0) {
    console.log('\n❌ Errors:');
    status.errors.forEach(error => console.log(`   - ${error}`));
    console.log('\n💡 Make sure all required APIs are enabled in Google Cloud Console');
  } else {
    console.log('\n🎉 All APIs are working correctly!');
  }

  return status;
};
