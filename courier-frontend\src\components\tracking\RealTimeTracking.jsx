import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Chip,
  Grid,
  Button,
  CircularProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  LocationOn,
  DirectionsCar,
  Schedule,
  Phone,
  Email,
  Refresh,
  Navigation,
  Timeline
} from '@mui/icons-material';
import { collection, doc, onSnapshot, query, where, orderBy } from 'firebase/firestore';
import { db } from '../../firebase';
import { useLocationTracking, useLocationFeatures } from '../../hooks/useLocationTracking';
import EnhancedMap from '../maps/EnhancedMap';

const RealTimeTracking = ({ orderId, courierId, mode = 'order' }) => {
  const [orderData, setOrderData] = useState(null);
  const [courierData, setCourierData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [mapInstance, setMapInstance] = useState(null);
  const [directionsRenderer, setDirectionsRenderer] = useState(null);
  const [markers, setMarkers] = useState({});
  const [routeInfo, setRouteInfo] = useState(null);

  const unsubscribeRefs = useRef([]);

  // Map center and zoom
  const [mapCenter, setMapCenter] = useState({ lat: 19.0760, lng: 72.8777 }); // Mumbai coordinates
  const [mapZoom, setMapZoom] = useState(12);

  // Use location tracking hooks
  const {
    currentLocation: courierLocation,
    locationHistory,
    subscribeToUserLocation,
    isTracking: isCourierTracking
  } = useLocationTracking();

  const { calculateDistance, formatDistance } = useLocationFeatures();

  useEffect(() => {
    if (mode === 'order' && orderId) {
      fetchOrderData();
    } else if (mode === 'courier' && courierId) {
      fetchCourierData();
    }

    return () => {
      // Cleanup subscriptions
      unsubscribeRefs.current.forEach(unsubscribe => unsubscribe());
      if (locationTrackerRef.current) {
        locationTrackerRef.current.stopTracking();
      }
    };
  }, [orderId, courierId, mode]);

  const fetchOrderData = async () => {
    try {
      setLoading(true);
      
      // Subscribe to order updates
      const orderRef = doc(db, 'orders', orderId);
      const unsubscribeOrder = onSnapshot(orderRef, (doc) => {
        if (doc.exists()) {
          const data = doc.data();
          setOrderData({ id: doc.id, ...data });
          
          // If order has assigned courier, fetch courier data
          if (data.assignedCourierId || data.assignedCourier || data.courierId) {
            const assignedCourierId = data.assignedCourierId || data.assignedCourier || data.courierId;
            fetchCourierLocation(assignedCourierId);
          }
        } else {
          setError('Order not found');
        }
        setLoading(false);
      });

      unsubscribeRefs.current.push(unsubscribeOrder);
    } catch (err) {
      console.error('Error fetching order data:', err);
      setError('Failed to load order data');
      setLoading(false);
    }
  };

  const fetchCourierData = async () => {
    try {
      setLoading(true);
      
      // Subscribe to courier updates
      const courierRef = doc(db, 'users', courierId);
      const unsubscribeCourier = onSnapshot(courierRef, (doc) => {
        if (doc.exists()) {
          const data = doc.data();
          setCourierData({ id: doc.id, ...data });
          
          // Update courier location if available
          if (data.location) {
            setCourierLocation(data.location);
            setMapCenter(data.location);
          }
        } else {
          setError('Courier not found');
        }
        setLoading(false);
      });

      unsubscribeRefs.current.push(unsubscribeCourier);
      
      // Fetch courier location history
      fetchLocationHistory(courierId);
    } catch (err) {
      console.error('Error fetching courier data:', err);
      setError('Failed to load courier data');
      setLoading(false);
    }
  };

  const fetchCourierLocation = (assignedCourierId) => {
    // Subscribe to courier location updates
    const courierRef = doc(db, 'users', assignedCourierId);
    const unsubscribeCourierLocation = onSnapshot(courierRef, (doc) => {
      if (doc.exists()) {
        const data = doc.data();
        setCourierData({ id: doc.id, ...data });
        
        if (data.location) {
          setCourierLocation(data.location);
          updateMapWithLocations(data.location);
        }
      }
    });

    unsubscribeRefs.current.push(unsubscribeCourierLocation);
    
    // Fetch location history
    fetchLocationHistory(assignedCourierId);
  };

  const fetchLocationHistory = (courierId) => {
    // Subscribe to location history
    const locationHistoryRef = collection(db, 'locationHistory');
    const q = query(
      locationHistoryRef,
      where('courierId', '==', courierId),
      orderBy('timestamp', 'desc')
    );

    const unsubscribeHistory = onSnapshot(q, (snapshot) => {
      const history = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setLocationHistory(history.slice(0, 10)); // Keep last 10 locations
    });

    unsubscribeRefs.current.push(unsubscribeHistory);
  };

  const updateMapWithLocations = async (courierLoc) => {
    if (!mapInstance || !orderData) return;

    try {
      // Clear existing markers
      clearMarkers(Object.values(markers));

      const newMarkers = {};
      const markerPositions = [];

      // Add pickup marker
      if (orderData.pickupAddress && orderData.pickupCoordinates) {
        const pickupMarker = createAdvancedMarker({
          position: orderData.pickupCoordinates,
          map: mapInstance,
          type: 'pickup',
          title: `Pickup: ${orderData.pickupAddress}`,
          status: orderData.status === 'picked_up' ? 'completed' : 'pending'
        });
        if (pickupMarker) {
          newMarkers.pickup = pickupMarker;
          markerPositions.push(orderData.pickupCoordinates);
        }
      }

      // Add delivery marker
      if (orderData.deliveryAddress && orderData.deliveryCoordinates) {
        const deliveryMarker = createAdvancedMarker({
          position: orderData.deliveryCoordinates,
          map: mapInstance,
          type: 'delivery',
          title: `Delivery: ${orderData.deliveryAddress}`,
          status: orderData.status === 'delivered' ? 'completed' : 'pending'
        });
        if (deliveryMarker) {
          newMarkers.delivery = deliveryMarker;
          markerPositions.push(orderData.deliveryCoordinates);
        }
      }

      // Add courier marker with enhanced info
      if (courierLoc) {
        const courierMarker = createAdvancedMarker({
          position: {
            lat: courierLoc.lat || courierLoc.latitude,
            lng: courierLoc.lng || courierLoc.longitude
          },
          map: mapInstance,
          type: 'courier',
          title: `${courierData?.name || 'Courier'} - ${isCourierTracking ? 'Live' : 'Last Known'} Location`,
          status: isCourierTracking ? 'active' : 'inactive',
          data: {
            accuracy: courierLoc.accuracy,
            speed: courierLoc.speed,
            timestamp: courierLoc.timestamp
          }
        });
        if (courierMarker) {
          newMarkers.courier = courierMarker;
          markerPositions.push({
            lat: courierLoc.lat || courierLoc.latitude,
            lng: courierLoc.lng || courierLoc.longitude
          });
        }
      }

      setMarkers(newMarkers);

      // Calculate route information using enhanced distance calculation
      if (courierLoc && orderData.deliveryCoordinates) {
        const courierPos = {
          lat: courierLoc.lat || courierLoc.latitude,
          lng: courierLoc.lng || courierLoc.longitude
        };

        const distance = calculateDistance(courierPos, orderData.deliveryCoordinates);
        const estimatedDuration = Math.round(distance / 1000 * 3); // Rough estimate: 3 minutes per km

        setRouteInfo({
          distance: formatDistance(distance),
          duration: `${estimatedDuration} min`,
          distanceValue: distance,
          durationValue: estimatedDuration * 60,
          isEstimate: true
        });

        // Try to get actual route from Google Directions API
        if (window.google && window.google.maps) {
          try {
            const directionsService = new window.google.maps.DirectionsService();

            directionsService.route({
              origin: courierPos,
              destination: orderData.deliveryCoordinates,
              travelMode: window.google.maps.TravelMode.DRIVING,
              unitSystem: window.google.maps.UnitSystem.METRIC,
              avoidHighways: false,
              avoidTolls: false
            }, (result, status) => {
              if (status === 'OK' && result) {
                // Clear existing directions
                if (directionsRenderer) {
                  directionsRenderer.setMap(null);
                }

                // Create new directions renderer
                const renderer = new window.google.maps.DirectionsRenderer({
                  map: mapInstance,
                  suppressMarkers: true,
                  polylineOptions: {
                    strokeColor: '#4285F4',
                    strokeWeight: 4,
                    strokeOpacity: 0.8
                  }
                });

                renderer.setDirections(result);
                setDirectionsRenderer(renderer);

                // Update route info with actual data
                const leg = result.routes[0].legs[0];
                setRouteInfo({
                  distance: leg.distance.text,
                  duration: leg.duration.text,
                  distanceValue: leg.distance.value,
                  durationValue: leg.duration.value,
                  isEstimate: false
                });
              }
            });
          } catch (routeError) {
            console.error('Error calculating route:', routeError);
          }
        }
      }

      // Adjust map bounds to show all markers
      if (markerPositions.length > 0) {
        const bounds = new window.google.maps.LatLngBounds();
        markerPositions.forEach(pos => {
          bounds.extend(new window.google.maps.LatLng(pos.lat, pos.lng));
        });

        // Add some padding
        mapInstance.fitBounds(bounds, { padding: 50 });
      }
    } catch (error) {
      console.error('Error updating map:', error);
      setError('Failed to update map display');
    }
  };

  const refreshLocation = () => {
    if (orderData?.assignedCourierId) {
      fetchCourierLocation(orderData.assignedCourierId);
    } else if (courierId) {
      fetchCourierData();
    }
  };

  // Get markers for the enhanced map
  const getMapMarkers = () => {
    const markers = [];

    // Add pickup marker
    if (orderData?.pickupAddress && orderData?.pickupCoordinates) {
      markers.push({
        position: orderData.pickupCoordinates,
        type: 'pickup',
        title: `Pickup: ${orderData.pickupAddress}`,
        status: orderData.status === 'picked_up' ? 'completed' : 'pending',
        data: {
          address: orderData.pickupAddress,
          type: 'pickup'
        }
      });
    }

    // Add delivery marker
    if (orderData?.deliveryAddress && orderData?.deliveryCoordinates) {
      markers.push({
        position: orderData.deliveryCoordinates,
        type: 'delivery',
        title: `Delivery: ${orderData.deliveryAddress}`,
        status: orderData.status === 'delivered' ? 'completed' : 'pending',
        data: {
          address: orderData.deliveryAddress,
          type: 'delivery'
        }
      });
    }

    // Add courier marker
    if (courierLocation) {
      markers.push({
        position: {
          lat: courierLocation.lat || courierLocation.latitude,
          lng: courierLocation.lng || courierLocation.longitude
        },
        type: 'courier',
        title: `${courierData?.name || courierData?.fullName || 'Courier'} - ${isCourierTracking ? 'Live' : 'Last Known'} Location`,
        status: isCourierTracking ? 'active' : 'inactive',
        data: {
          accuracy: courierLocation.accuracy,
          speed: courierLocation.speed,
          timestamp: courierLocation.timestamp,
          courier: courierData
        }
      });
    }

    return markers;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending': return 'warning';
      case 'processing': return 'info';
      case 'picked_up': return 'primary';
      case 'in_transit': return 'secondary';
      case 'delivered': return 'success';
      case 'cancelled': return 'error';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleString();
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h5" gutterBottom>
          {mode === 'order' ? `Order Tracking #${orderData?.orderNumber || orderId}` : `Courier Tracking - ${courierData?.name}`}
        </Typography>
        <Tooltip title="Refresh Location">
          <IconButton onClick={refreshLocation} color="primary">
            <Refresh />
          </IconButton>
        </Tooltip>
      </Box>

      <Grid container spacing={3}>
        {/* Order/Courier Information */}
        <Grid item xs={12} md={4}>
          {mode === 'order' && orderData && (
            <Card sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Order Information
                </Typography>
                <Box display="flex" alignItems="center" mb={1}>
                  <Chip 
                    label={orderData.status} 
                    color={getStatusColor(orderData.status)}
                    size="small"
                  />
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Customer: {orderData.customerName}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Phone: {orderData.customerPhone}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Created: {formatTimestamp(orderData.createdAt)}
                </Typography>
                {orderData.deliveryAddress && (
                  <Typography variant="body2" color="text.secondary">
                    Delivery: {orderData.deliveryAddress}
                  </Typography>
                )}
              </CardContent>
            </Card>
          )}

          {courierData && (
            <Card sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Courier Information
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {courierData.name || courierData.displayName}
                </Typography>
                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <Phone fontSize="small" />
                  <Typography variant="body2">{courierData.phone}</Typography>
                </Box>
                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <Email fontSize="small" />
                  <Typography variant="body2">{courierData.email}</Typography>
                </Box>
                <Box display="flex" alignItems="center" gap={1}>
                  <Chip 
                    label={courierData.status || 'available'} 
                    color={courierData.status === 'available' ? 'success' : 'warning'}
                    size="small"
                  />
                  {courierData.isOnline && (
                    <Chip label="Online" color="primary" size="small" />
                  )}
                </Box>
              </CardContent>
            </Card>
          )}

          {/* Route Information */}
          {routeInfo && (
            <Card sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Route Information
                </Typography>
                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <Navigation fontSize="small" />
                  <Typography variant="body2">Distance: {routeInfo.distance}</Typography>
                </Box>
                <Box display="flex" alignItems="center" gap={1}>
                  <Schedule fontSize="small" />
                  <Typography variant="body2">ETA: {routeInfo.duration}</Typography>
                </Box>
              </CardContent>
            </Card>
          )}

          {/* Location History */}
          {locationHistory.length > 0 && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recent Locations
                </Typography>
                <List dense>
                  {locationHistory.slice(0, 5).map((location, index) => (
                    <ListItem key={location.id} divider={index < 4}>
                      <ListItemIcon>
                        <LocationOn fontSize="small" />
                      </ListItemIcon>
                      <ListItemText
                        primary={`${location.location?.lat?.toFixed(4)}, ${location.location?.lng?.toFixed(4)}`}
                        secondary={formatTimestamp(location.timestamp)}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* Map */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ height: 600, position: 'relative', borderRadius: 2, overflow: 'hidden' }}>
            <EnhancedMap
              center={mapCenter}
              zoom={mapZoom}
              markers={getMapMarkers()}
              showUserLocation={false}
              enableFullscreen={true}
              enableRefresh={true}
              enableMyLocation={false}
              height={600}
              onMapLoad={({ map }) => {
                setMapInstance(map);
                if (courierLocation) {
                  updateMapWithLocations(courierLocation);
                }
              }}
              onMarkerClick={(markerData) => {
                console.log('Marker clicked:', markerData);
              }}
              onRefresh={() => {
                refreshLocation();
              }}
            />
            {!courierLocation && (
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  textAlign: 'center',
                  backgroundColor: 'rgba(255,255,255,0.9)',
                  p: 2,
                  borderRadius: 1,
                  zIndex: 1000
                }}
              >
                <Typography variant="body1" color="text.secondary">
                  {mode === 'order' ? 'Waiting for courier location...' : 'No location data available'}
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default RealTimeTracking;
